<?php
/**
 * Repository pro práci s technologiemi
 */

declare(strict_types=1);

namespace PrestaShop\Module\Technologie\Repository;

use Doctrine\ORM\EntityRepository;
use PrestaShop\Module\Technologie\Entity\Technologie;

class TechnologieRepository extends EntityRepository
{
    /**
     * Najde všechny aktivní technologie seřazené podle pozice
     */
    public function findActiveOrderedByPosition(): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.active = :active')
            ->setParameter('active', true)
            ->orderBy('t.position', 'ASC')
            ->addOrderBy('t.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Najde všechny technologie pro admin (včetně neaktivních)
     */
    public function findAllOrderedByPosition(): array
    {
        return $this->createQueryBuilder('t')
            ->orderBy('t.position', 'ASC')
            ->addOrderBy('t.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Najde technologii podle ID
     */
    public function findOneById(int $id): ?Technologie
    {
        return $this->find($id);
    }

    /**
     * Uloží technologii
     */
    public function save(Technologie $technologie): void
    {
        $this->getEntityManager()->persist($technologie);
        $this->getEntityManager()->flush();
    }

    /**
     * Smaže technologii
     */
    public function delete(Technologie $technologie): void
    {
        $this->getEntityManager()->remove($technologie);
        $this->getEntityManager()->flush();
    }

    /**
     * Získá nejvyšší pozici pro novou technologii
     */
    public function getMaxPosition(): int
    {
        $result = $this->createQueryBuilder('t')
            ->select('MAX(t.position)')
            ->getQuery()
            ->getSingleScalarResult();
            
        return (int) $result;
    }

    /**
     * Hromadná aktivace/deaktivace
     */
    public function bulkUpdateActive(array $ids, bool $active): int
    {
        return $this->createQueryBuilder('t')
            ->update()
            ->set('t.active', ':active')
            ->set('t.dateUpd', ':dateUpd')
            ->where('t.id IN (:ids)')
            ->setParameter('active', $active)
            ->setParameter('dateUpd', new \DateTime())
            ->setParameter('ids', $ids)
            ->getQuery()
            ->execute();
    }

    /**
     * Aktualizace pozic pro drag & drop řazení
     */
    public function updatePositions(array $positions): void
    {
        foreach ($positions as $id => $position) {
            $this->createQueryBuilder('t')
                ->update()
                ->set('t.position', ':position')
                ->set('t.dateUpd', ':dateUpd')
                ->where('t.id = :id')
                ->setParameter('position', $position)
                ->setParameter('dateUpd', new \DateTime())
                ->setParameter('id', $id)
                ->getQuery()
                ->execute();
        }
    }

    /**
     * Najde technologii podle slug
     */
    public function findBySlug(string $slug): ?Technologie
    {
        return $this->createQueryBuilder('t')
            ->where('t.slug = :slug')
            ->setParameter('slug', $slug)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Najde aktivní technologii podle slug
     */
    public function findActiveBySlug(string $slug): ?Technologie
    {
        return $this->createQueryBuilder('t')
            ->where('t.slug = :slug')
            ->andWhere('t.active = :active')
            ->setParameter('slug', $slug)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Získá seznam všech slug pro validaci
     */
    public function getAllSlugs(): array
    {
        $result = $this->createQueryBuilder('t')
            ->select('t.slug')
            ->where('t.slug IS NOT NULL')
            ->andWhere('t.slug != :empty')
            ->setParameter('empty', '')
            ->getQuery()
            ->getScalarResult();

        return array_column($result, 'slug');
    }

    /**
     * Kontrola existence slug (pro validaci unikátnosti)
     */
    public function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $qb = $this->createQueryBuilder('t')
            ->select('COUNT(t.id)')
            ->where('t.slug = :slug')
            ->setParameter('slug', $slug);

        if ($excludeId !== null) {
            $qb->andWhere('t.id != :excludeId')
               ->setParameter('excludeId', $excludeId);
        }

        return (int) $qb->getQuery()->getSingleScalarResult() > 0;
    }

    /**
     * Najde technologie s prázdným slug (pro upgrade)
     */
    public function findWithEmptySlug(): array
    {
        return $this->createQueryBuilder('t')
            ->where('t.slug IS NULL OR t.slug = :empty')
            ->setParameter('empty', '')
            ->getQuery()
            ->getResult();
    }
}
