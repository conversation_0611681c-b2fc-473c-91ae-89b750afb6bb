<?php
/**
 * Front Office controller pro zobrazení technologií potisku
 *
 * <AUTHOR> SA <<EMAIL>>
 * @copyright 2024 PrestaShop SA
 * @license   https://opensource.org/licenses/AFL-3.0 Academic Free License 3.0 (AFL-3.0)
 */



// Načtení autoloaderu modulu
require_once _PS_MODULE_DIR_ . 'technologie/technologie.php';

use PrestaShop\Module\Technologie\Entity\Technologie as TechnologieEntity;
use PrestaShop\Module\Technologie\Repository\TechnologieRepository;
use PrestaShop\Module\Technologie\Repository\TechnologieDbRepository;

class TechnologieTechnologieModuleFrontController extends ModuleFrontController
{
    private $technologieRepository = null;

    /**
     * @var array|null Aktuální technologie pro breadcrumb
     */
    private $currentTechnologie = null;

    /**
     * Konstruktor controlleru
     */
    public function __construct()
    {
        parent::__construct();
        // Repository se inicializuje lazy v getTechnologie() metodě
    }

    /**
     * Inicializace controlleru - nastavení základních parametrů
     */
    public function init()
    {
        parent::init();

        // Kontrola a redirect nesprávných URL
        $this->checkAndRedirectOldUrls();

        // Nastavení meta informací pro SEO
        $this->context->smarty->assign([
            'meta_title' => $this->getMetaTitle(),
            'meta_description' => $this->getMetaDescription(),
            'meta_keywords' => $this->getMetaKeywords(),
            'canonical_url' => $this->getCanonicalURL()
        ]);
    }

    /**
     * Inicializace obsahu stránky
     */
    public function initContent()
    {
        parent::initContent();

        // Kontrola zda se jedná o detail stránku
        $slug = Tools::getValue('slug');
        $action = Tools::getValue('action');

        // Debug informace pro diagnostiku
        if (Tools::getValue('debug') === '1') {
            $this->debugAction();
            return;
        }

        if ($slug) {
            // Detail technologie
            $this->detailAction();
        } else {
            // Seznam technologií
            $this->listAction();
        }
    }

    /**
     * Akce pro seznam technologií
     */
    public function listAction()
    {
        try {
            // Načtení aktivních technologií
            $technologie = $this->getTechnologie();

            // Příprava dat pro šablonu
            $this->context->smarty->assign([
                'technologie' => $technologie,
                'page_title' => 'Technologie potisku',
                'page_description' => 'Přehled všech dostupných technologií potisku a jejich vlastností',
                'module_dir' => _MODULE_DIR_ . 'technologie/',
                'upload_dir' => _MODULE_DIR_ . 'technologie/uploads/',
                'has_technologie' => !empty($technologie),
                'debug_mode' => Tools::getValue('debug') === '1'
            ]);

            // Breadcrumb se nastaví automaticky přes getBreadcrumbLinks()

            // Přidání CSS a JS assets
            $this->addAssets();

            // Nastavení šablony pro zobrazení
            $this->setTemplate('module:technologie/views/templates/front/technologie.tpl');

        } catch (\Exception $e) {
            // Logování chyby do PrestaShop logu
            PrestaShopLogger::addLog(
                'Technologie module error: ' . $e->getMessage(),
                3,
                null,
                'Technologie',
                null,
                true
            );

            // Zobrazení chybové stránky uživateli
            $this->context->smarty->assign([
                'error_message' => 'Omlouváme se, došlo k chybě při načítání technologií.',
                'page_title' => 'Chyba při načítání'
            ]);

            $this->setTemplate('module:technologie/views/templates/front/error.tpl');
        }
    }

    /**
     * Načtení technologií - používáme přímo databázové dotazy pro zajištění správného nastavení URL
     */
    private function getTechnologie()
    {
        // Pro zajištění správného nastavení detail_url používáme přímo databázové dotazy
        return $this->getTechnologieFromDatabase();
    }

    /**
     * Fallback metoda pro načtení technologií z databáze
     */
    private function getTechnologieFromDatabase()
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'technologie`
                WHERE active = 1
                ORDER BY position ASC, name ASC';

        $results = Db::getInstance()->executeS($sql);

        if (!$results) {
            return [];
        }

        // Převod na objekty pro kompatibilitu se šablonou
        $technologie = [];
        foreach ($results as $row) {
            $tech = new stdClass();
            $tech->id = (int)$row['id_technologie'];
            $tech->name = $row['name'];
            $tech->description = $row['description'];
            $tech->image = $row['image'];
            $tech->position = (int)$row['position'];
            $tech->active = (bool)$row['active'];

            // Nové vlastnosti pro detail
            $tech->slug = $row['slug'] ?? null;
            $tech->advantages = $row['advantages'] ?? null;
            $tech->applications = $row['applications'] ?? null;
            $tech->detailed_description = $row['detailed_description'] ?? null;

            // Metody pro kompatibilitu
            $tech->getId = function() use ($tech) { return $tech->id; };
            $tech->getName = function() use ($tech) { return $tech->name; };
            $tech->getDescription = function() use ($tech) { return $tech->description; };
            $tech->getImage = function() use ($tech) { return $tech->image; };
            $tech->getPosition = function() use ($tech) { return $tech->position; };
            $tech->isActive = function() use ($tech) { return $tech->active; };
            $tech->getSlug = function() use ($tech) { return $tech->slug; };
            $tech->getAdvantages = function() use ($tech) { return $tech->advantages; };
            $tech->getApplications = function() use ($tech) { return $tech->applications; };
            $tech->getDetailedDescription = function() use ($tech) { return $tech->detailed_description; };

            $tech->getImageUrl = function() use ($tech) {
                return $tech->image ? _MODULE_DIR_ . 'technologie/uploads/' . $tech->image : null;
            };

            // Přímé nastavení URL detailu
            if ($tech->slug) {
                // Vždy použijeme custom URL ve formátu /reklamni-potisk/slug
                $tech->detail_url = $this->generateCustomUrl($tech->slug);

                // Debug informace
                $tech->debug_info = [
                    'slug' => $tech->slug,
                    'final_url' => $tech->detail_url,
                    'generation_method' => 'custom'
                ];

                // Debug log pro každou technologii
                PrestaShopLogger::addLog(
                    'Technologie: URL for ' . $tech->name . ' (slug: ' . $tech->slug . ') - Final: ' . $tech->detail_url,
                    1,
                    null,
                    'Technologie'
                );
            } else {
                $tech->detail_url = null;
                $tech->debug_info = ['error' => 'No slug available', 'slug_value' => $tech->slug];
            }

            // Metoda pro získání URL detailu (pro kompatibilitu)
            $tech->getDetailUrl = function() use ($tech) {
                return $tech->detail_url;
            };

            // Metoda pro získání preview výhod (první 3)
            $tech->getAdvantagesPreview = function() use ($tech) {
                if (!$tech->advantages) {
                    return [];
                }
                $advantages = explode("\n", $tech->advantages);
                $advantages = array_filter(array_map('trim', $advantages));
                return array_slice($advantages, 0, 3);
            };

            $technologie[] = $tech;
        }

        return $technologie;
    }

    /**
     * Akce pro detail technologie
     */
    public function detailAction()
    {
        $slug = Tools::getValue('slug');

        if (!$slug) {
            Tools::redirect($this->generateCustomUrl());
            return;
        }

        try {
            // Načtení technologie podle slug
            $technologie = $this->getTechnologieBySlug($slug);

            if (!$technologie) {
                // 404 pro neexistující technologii
                header('HTTP/1.0 404 Not Found');
                $this->context->smarty->assign([
                    'error_message' => 'Požadovaná technologie nebyla nalezena.',
                    'page_title' => 'Technologie nenalezena'
                ]);
                $this->setTemplate('module:technologie/views/templates/front/error.tpl');
                return;
            }

            // Nastavíme aktuální technologii pro breadcrumb
            $this->currentTechnologie = $technologie;

            // Příprava dat pro šablonu
            $this->context->smarty->assign([
                'technologie' => $technologie,
                'page_title' => $technologie['name'] . ' - Technologie potisku',
                'page_description' => $technologie['description'] ?: 'Detailní informace o technologii ' . $technologie['name'],
                'module_dir' => _MODULE_DIR_ . 'technologie/',
                'upload_dir' => _MODULE_DIR_ . 'technologie/uploads/',
                'gallery_dir' => _MODULE_DIR_ . 'technologie/uploads/gallery/',
                'advantages_array' => $this->parseTextToArray($technologie['advantages']),
                'applications_array' => $this->parseTextToArray($technologie['applications']),
                'gallery_images' => $this->parseGalleryImages($technologie['gallery_images']),
                'detail_url' => $this->generateCustomUrl($slug)
            ]);

            // Breadcrumb se nastaví automaticky přes getBreadcrumbLinks()

            // Nastavení SEO meta tagů
            $this->setDetailMetaTags($technologie);

            // Přidání CSS a JS assets
            $this->addAssets();

            // Nastavení šablony pro detail
            $this->setTemplate('module:technologie/views/templates/front/technologie-detail.tpl');

        } catch (\Exception $e) {
            PrestaShopLogger::addLog(
                'Technologie detail error: ' . $e->getMessage(),
                2,
                null,
                'Technologie'
            );

            // Fallback na error stránku
            header('HTTP/1.0 500 Internal Server Error');
            $this->context->smarty->assign([
                'error_message' => 'Došlo k chybě při načítání technologie.',
                'page_title' => 'Chyba serveru'
            ]);
            $this->setTemplate('module:technologie/views/templates/front/error.tpl');
        }
    }

    /**
     * Přepisujeme getBreadcrumbLinks pro správné breadcrumb v PrestaShop
     */
    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();

        // Přidáme breadcrumb pro technologie
        $breadcrumb['links'][] = [
            'title' => 'Technologie potisku',
            'url' => $this->generateCustomUrl()
        ];

        // Pokud jsme na detail stránce, přidáme název technologie
        $slug = Tools::getValue('slug');
        if ($slug && isset($this->currentTechnologie)) {
            $breadcrumb['links'][] = [
                'title' => $this->currentTechnologie['name'],
                'url' => ''
            ];
        }

        return $breadcrumb;
    }

    /**
     * Přidání CSS a JS souborů
     */
    private function addAssets()
    {
        // FontAwesome pro ikonky
        $this->addCSS('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

        // CSS pro front office
        if (file_exists(_PS_MODULE_DIR_ . 'technologie/views/css/front.css')) {
            $this->addCSS(_MODULE_DIR_ . 'technologie/views/css/front.css');
        }

        // JavaScript pro interaktivitu (pokud bude potřeba)
        if (file_exists(_PS_MODULE_DIR_ . 'technologie/views/js/front.js')) {
            $this->addJS(_MODULE_DIR_ . 'technologie/views/js/front.js');
        }

        // Bootstrap pokud není načten v tématu
        if (!$this->isBootstrapLoaded()) {
            $this->addCSS('https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css');
        }
    }

    /**
     * Kontrola zda je Bootstrap načten v tématu
     */
    private function isBootstrapLoaded()
    {
        // Jednoduchá kontrola - předpokládáme, že Bootstrap není načten
        // V reálné implementaci by bylo lepší zkontrolovat skutečně načtené CSS soubory
        return false;
    }

    /**
     * Získání meta title pro SEO
     */
    private function getMetaTitle()
    {
        return 'Technologie potisku - ' . Configuration::get('PS_SHOP_NAME');
    }

    /**
     * Získání meta description pro SEO
     */
    private function getMetaDescription()
    {
        return 'Přehled všech dostupných technologií potisku. Sítotisk, digitální potisk, vyšívání, termotransfer a další moderní techniky pro potisk textilu a reklamních předmětů.';
    }

    /**
     * Získání meta keywords pro SEO
     */
    private function getMetaKeywords()
    {
        return 'technologie potisku, sítotisk, digitální potisk, vyšívání, termotransfer, potisk textilu, reklamní předměty';
    }

    /**
     * Získání kanonické URL
     */
    public function getCanonicalURL()
    {
        return $this->generateCustomUrl();
    }

    /**
     * Nastavení HTTP hlaviček a Open Graph meta tagů
     */
    public function setMedia()
    {
        parent::setMedia();

        // Přidání Open Graph meta tagů pro sociální sítě
        $this->context->smarty->assign([
            'og_title' => $this->getMetaTitle(),
            'og_description' => $this->getMetaDescription(),
            'og_url' => $this->getCanonicalURL(),
            'og_type' => 'website',
            'og_image' => 'http' . (Tools::usingSecureMode() ? 's' : '') . '://' . Tools::getHttpHost(false) . _MODULE_DIR_ . 'technologie/views/img/og-image.jpg'
        ]);
    }

    /**
     * Načtení technologie podle slug
     */
    private function getTechnologieBySlug(string $slug): ?array
    {
        // Lazy inicializace repository (stejná logika jako v getTechnologie)
        if ($this->technologieRepository === null) {
            try {
                // Pokus o inicializaci Doctrine repository
                if (method_exists($this, 'get')) {
                    $entityManager = $this->get('doctrine.orm.entity_manager');
                    if ($entityManager) {
                        $this->technologieRepository = $entityManager->getRepository(TechnologieEntity::class);
                    }
                }
            } catch (\Exception $e) {
                // Doctrine není dostupné, použijeme fallback
                PrestaShopLogger::addLog(
                    'Technologie module: Failed to initialize repository: ' . $e->getMessage(),
                    2,
                    null,
                    'Technologie'
                );
                // Nastavíme fallback repository
                $this->technologieRepository = new TechnologieDbRepository();
            }

            // Pokud se nepodařilo inicializovat Doctrine, použijeme fallback
            if ($this->technologieRepository === null) {
                $this->technologieRepository = new TechnologieDbRepository();
            }
        }

        if ($this->technologieRepository) {
            try {
                $entity = $this->technologieRepository->findActiveBySlug($slug);
                if ($entity) {
                    return $this->convertEntityToArray($entity);
                }
            } catch (\Exception $e) {
                PrestaShopLogger::addLog(
                    'Technologie repository error in getTechnologieBySlug: ' . $e->getMessage(),
                    2,
                    null,
                    'Technologie'
                );
            }
        }

        // Fallback - přímý databázový dotaz
        return $this->getTechnologieBySlugFromDatabase($slug);
    }

    /**
     * Fallback načtení technologie podle slug z databáze
     */
    private function getTechnologieBySlugFromDatabase(string $slug): ?array
    {
        try {
            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'technologie`
                    WHERE `slug` = "' . pSQL($slug) . '" AND `active` = 1';

            $result = Db::getInstance()->getRow($sql);

            if ($result) {
                // Konverze na stejný formát jako entity
                $data = [
                    'id' => (int)$result['id_technologie'],
                    'name' => $result['name'],
                    'description' => $result['description'],
                    'detailed_description' => $result['detailed_description'],
                    'advantages' => $result['advantages'],
                    'applications' => $result['applications'],
                    'gallery_images' => $result['gallery_images'],
                    'image' => $result['image'],
                    'slug' => $result['slug'],
                    'position' => (int)$result['position'],
                    'active' => (bool)$result['active'],
                    'date_add' => $result['date_add'],
                    'date_upd' => $result['date_upd']
                ];

                // Přidání image_path pro detail šablonu
                $data['image_path'] = $result['image'] ? _MODULE_DIR_ . 'technologie/uploads/' . $result['image'] : null;

                return $data;
            }
        } catch (\Exception $e) {
            PrestaShopLogger::addLog(
                'Database error in getTechnologieBySlugFromDatabase: ' . $e->getMessage(),
                2,
                null,
                'Technologie'
            );
        }

        return null;
    }

    /**
     * Konverze entity na array pro šablonu
     */
    private function convertEntityToArray($entity): array
    {
        if (is_array($entity)) {
            return $entity;
        }

        // Pokud je to Doctrine entity
        $data = [
            'id' => $entity->getId(),
            'name' => $entity->getName(),
            'description' => $entity->getDescription(),
            'detailed_description' => $entity->getDetailedDescription(),
            'advantages' => $entity->getAdvantages(),
            'applications' => $entity->getApplications(),
            'gallery_images' => $entity->getGalleryImages(),
            'image' => $entity->getImage(),
            'slug' => $entity->getSlug(),
            'position' => $entity->getPosition(),
            'active' => $entity->isActive(),
            'date_add' => $entity->getDateAdd() ? $entity->getDateAdd()->format('Y-m-d H:i:s') : null,
            'date_upd' => $entity->getDateUpd() ? $entity->getDateUpd()->format('Y-m-d H:i:s') : null
        ];

        // Přidání image_path pro detail šablonu
        $data['image_path'] = $entity->getImage() ? _MODULE_DIR_ . 'technologie/uploads/' . $entity->getImage() : null;

        return $data;
    }



    /**
     * Nastavení SEO meta tagů pro detail technologie
     */
    private function setDetailMetaTags(array $technologie): void
    {
        // Meta title
        $metaTitle = $technologie['name'] . ' - Technologie potisku - ' . Configuration::get('PS_SHOP_NAME');
        $this->context->smarty->assign('meta_title', $metaTitle);

        // Meta description
        $metaDescription = $technologie['description'] ?: 'Detailní informace o technologii ' . $technologie['name'] . '. Výhody, oblasti použití a galerie realizací.';
        if (strlen($metaDescription) > 160) {
            $metaDescription = substr($metaDescription, 0, 157) . '...';
        }
        $this->context->smarty->assign('meta_description', $metaDescription);

        // Meta keywords
        $keywords = ['technologie potisku', $technologie['name']];
        if ($technologie['advantages']) {
            $advantages = $this->parseTextToArray($technologie['advantages']);
            $keywords = array_merge($keywords, array_slice($advantages, 0, 3));
        }
        $this->context->smarty->assign('meta_keywords', implode(', ', $keywords));

        // Open Graph tagy pro detail
        $detailUrl = $this->generateCustomUrl($technologie['slug']);
        $ogImage = $technologie['image'] ?
            'http' . (Tools::usingSecureMode() ? 's' : '') . '://' . Tools::getHttpHost(false) . _MODULE_DIR_ . 'technologie/uploads/' . $technologie['image'] :
            'http' . (Tools::usingSecureMode() ? 's' : '') . '://' . Tools::getHttpHost(false) . _MODULE_DIR_ . 'technologie/views/img/og-image.jpg';

        $this->context->smarty->assign([
            'og_title' => $metaTitle,
            'og_description' => $metaDescription,
            'og_url' => $detailUrl,
            'og_type' => 'article',
            'og_image' => $ogImage
        ]);
    }

    /**
     * Parsování textu na array (řádky oddělené \n)
     */
    private function parseTextToArray(?string $text): array
    {
        if (!$text) {
            return [];
        }

        $lines = explode("\n", $text);
        return array_filter(array_map('trim', $lines));
    }

    /**
     * Parsování JSON galerie obrázků
     */
    private function parseGalleryImages(?string $galleryJson): array
    {
        if (!$galleryJson) {
            return [];
        }

        $images = json_decode($galleryJson, true);
        if (!is_array($images)) {
            return [];
        }

        // Ověření existence souborů a příprava cest
        $validImages = [];
        foreach ($images as $image) {
            if (is_string($image) && file_exists(_PS_MODULE_DIR_ . 'technologie/uploads/gallery/' . $image)) {
                $validImages[] = [
                    'filename' => $image,
                    'path' => _MODULE_DIR_ . 'technologie/uploads/gallery/' . $image,
                    'url' => 'http' . (Tools::usingSecureMode() ? 's' : '') . '://' . Tools::getHttpHost(false) . _MODULE_DIR_ . 'technologie/uploads/gallery/' . $image
                ];
            }
        }

        return $validImages;
    }

    /**
     * Debug akce pro diagnostiku problémů s routing
     */
    private function debugAction()
    {
        // Získání všech technologií pro debug
        $technologie = $this->getTechnologieFromDatabase();

        $debugInfo = [
            'current_url' => $_SERVER['REQUEST_URI'] ?? 'N/A',
            'slug_param' => Tools::getValue('slug'),
            'action_param' => Tools::getValue('action'),
            'module_routes' => $this->getModuleRoutes(),
            'available_technologies' => [],
            'correct_urls' => []
        ];

        // Příprava seznamu technologií a jejich správných URL
        foreach ($technologie as $tech) {
            $debugInfo['available_technologies'][] = [
                'id' => $tech->id,
                'name' => $tech->name,
                'slug' => $tech->slug,
                'active' => $tech->active
            ];
        }

        // Získání správných URL
        $debugInfo['correct_urls'] = $this->getCorrectUrls();

        // Příprava dat pro debug šablonu
        $this->context->smarty->assign([
            'debug_info' => $debugInfo,
            'page_title' => 'Debug - Technologie modul',
            'module_dir' => _MODULE_DIR_ . 'technologie/'
        ]);

        // Nastavení debug šablony
        $this->setTemplate('module:technologie/views/templates/front/debug.tpl');
    }

    /**
     * Získání informací o module routes
     */
    private function getModuleRoutes()
    {
        $listUrl = $this->generateCustomUrl();
        $detailUrl = $this->generateCustomUrl('sitotisk');

        return [
            'list_route' => [
                'pattern' => '/reklamni-potisk',
                'example' => $listUrl
            ],
            'detail_route' => [
                'pattern' => '/reklamni-potisk/{slug}',
                'example' => $detailUrl
            ]
        ];
    }

    /**
     * Kontrola a redirect starých/nesprávných URL
     */
    private function checkAndRedirectOldUrls()
    {
        $currentUrl = $_SERVER['REQUEST_URI'] ?? '';

        // Kontrola starého formátu URL: /modules/technologie/detail/{slug}
        if (preg_match('#/modules/technologie/detail/([a-zA-Z0-9\-]+)#', $currentUrl, $matches)) {
            $slug = $matches[1];

            // Redirect na správnou URL
            $correctUrl = $this->generateCustomUrl($slug);

            // 301 redirect pro SEO
            header('HTTP/1.1 301 Moved Permanently');
            header('Location: ' . $correctUrl);
            exit;
        }

        // Kontrola jiných nesprávných formátů
        if (preg_match('#/modules/technologie/([a-zA-Z0-9\-]+)#', $currentUrl, $matches)) {
            $slug = $matches[1];

            // Pokud to není 'technologie' controller, může to být slug
            if ($slug !== 'technologie') {
                $correctUrl = $this->generateCustomUrl($slug);

                header('HTTP/1.1 301 Moved Permanently');
                header('Location: ' . $correctUrl);
                exit;
            }
        }
    }

    /**
     * Generování custom URL pro detail technologie
     */
    private function generateCustomUrl($slug = null)
    {
        try {
            // Nejprve zkusíme fallback - manuální sestavení URL
            $baseUrl = Tools::getHttpHost(true);
            if ($slug) {
                $customUrl = $baseUrl . '/reklamni-potisk/' . $slug;
            } else {
                $customUrl = $baseUrl . '/reklamni-potisk';
            }

            // Debug log
            PrestaShopLogger::addLog(
                'Technologie: Generated custom URL for slug "' . ($slug ?: 'list') . '": ' . $customUrl,
                1,
                null,
                'Technologie'
            );

            return $customUrl;

        } catch (Exception $e) {
            PrestaShopLogger::addLog(
                'Technologie: Error generating custom URL: ' . $e->getMessage(),
                2,
                null,
                'Technologie'
            );

            // Fallback na standardní URL - už nepoužíváme getModuleLink
            if ($slug) {
                return Tools::getHttpHost(true) . '/reklamni-potisk/' . $slug;
            } else {
                return Tools::getHttpHost(true) . '/reklamni-potisk';
            }
        }
    }

    /**
     * Aktualizace URL v debug informacích
     */
    private function getCorrectUrls()
    {
        $technologie = $this->getTechnologieFromDatabase();
        $urls = [];

        foreach ($technologie as $tech) {
            if ($tech->slug) {
                $customUrl = $this->generateCustomUrl($tech->slug);
                $urls[] = [
                    'name' => $tech->name,
                    'slug' => $tech->slug,
                    'url' => $customUrl
                ];
            }
        }

        return $urls;
    }
}
